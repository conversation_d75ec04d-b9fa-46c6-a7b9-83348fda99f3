#!/usr/bin/env bun
/**
 * Basic test script for the OpenAI Responses API implementation
 * This script tests the new /v1/responses endpoints to ensure they work correctly
 */

import { config } from "../src/config";

const BASE_URL = `http://localhost:${config.port}`;
const PROXY_ENDPOINT = `${BASE_URL}${config.proxyBasePath}${config.proxyEndpointRoute}`;

interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  response?: any;
}

const results: TestResult[] = [];

function logTest(name: string, success: boolean, error?: string, response?: any) {
  results.push({ name, success, error, response });
  const status = success ? "✅ PASS" : "❌ FAIL";
  console.log(`${status} ${name}`);
  if (error) {
    console.log(`   Error: ${error}`);
  }
}

async function testResponsesAPIEndpoint() {
  console.log("🧪 Testing OpenAI Responses API Implementation\n");

  // Test 1: Basic responses API request with OpenAI model
  try {
    const response = await fetch(`${PROXY_ENDPOINT}/v1/responses`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-key", // This will likely fail without real keys
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo",
        input: "Hello, this is a test of the responses API",
        store: true,
      }),
    });

    if (response.status === 401 || response.status === 403) {
      logTest("OpenAI Responses API - Authentication", true, "Expected auth error (no real API key)");
    } else if (response.status === 400) {
      const errorData = await response.json();
      logTest("OpenAI Responses API - Request Format", true, "Request reached validation (expected with test key)");
    } else {
      logTest("OpenAI Responses API - Basic Request", response.ok, `Status: ${response.status}`);
    }
  } catch (error) {
    logTest("OpenAI Responses API - Basic Request", false, error.message);
  }

  // Test 2: Universal routing with Claude model (should transform to chat completions)
  try {
    const response = await fetch(`${PROXY_ENDPOINT}/v1/responses`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-key",
      },
      body: JSON.stringify({
        model: "claude-3-sonnet",
        input: "Hello, this is a test with Claude",
      }),
    });

    if (response.status === 501) {
      const errorData = await response.json();
      logTest("Universal Routing - Claude Transform", true, "Correctly returned 501 for unsupported responses API");
    } else {
      logTest("Universal Routing - Claude Transform", false, `Unexpected status: ${response.status}`);
    }
  } catch (error) {
    logTest("Universal Routing - Claude Transform", false, error.message);
  }

  // Test 3: Test response retrieval endpoint (GET)
  try {
    const response = await fetch(`${PROXY_ENDPOINT}/v1/responses/test-response-id`, {
      method: "GET",
      headers: {
        "Authorization": "Bearer test-key",
      },
    });

    if (response.status === 401 || response.status === 403 || response.status === 404) {
      logTest("Response Retrieval - GET endpoint", true, "Expected error (no real API key or response ID)");
    } else {
      logTest("Response Retrieval - GET endpoint", response.ok, `Status: ${response.status}`);
    }
  } catch (error) {
    logTest("Response Retrieval - GET endpoint", false, error.message);
  }

  // Test 4: Test response cancellation endpoint
  try {
    const response = await fetch(`${PROXY_ENDPOINT}/v1/responses/test-response-id/cancel`, {
      method: "POST",
      headers: {
        "Authorization": "Bearer test-key",
      },
    });

    if (response.status === 401 || response.status === 403 || response.status === 404) {
      logTest("Response Cancellation - POST endpoint", true, "Expected error (no real API key or response ID)");
    } else {
      logTest("Response Cancellation - POST endpoint", response.ok, `Status: ${response.status}`);
    }
  } catch (error) {
    logTest("Response Cancellation - POST endpoint", false, error.message);
  }

  // Test 5: Test invalid request format
  try {
    const response = await fetch(`${PROXY_ENDPOINT}/v1/responses`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-key",
      },
      body: JSON.stringify({
        // Missing required fields
        input: "Test without model",
      }),
    });

    if (response.status === 400) {
      logTest("Request Validation - Invalid Format", true, "Correctly rejected invalid request");
    } else {
      logTest("Request Validation - Invalid Format", false, `Expected 400, got ${response.status}`);
    }
  } catch (error) {
    logTest("Request Validation - Invalid Format", false, error.message);
  }

  // Summary
  console.log("\n📊 Test Summary:");
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  console.log(`${passed}/${total} tests passed`);

  if (passed === total) {
    console.log("🎉 All tests passed! The Responses API implementation appears to be working correctly.");
  } else {
    console.log("⚠️  Some tests failed. Check the implementation for issues.");
  }

  return passed === total;
}

// Run the tests
if (import.meta.main) {
  testResponsesAPIEndpoint()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error("Test runner failed:", error);
      process.exit(1);
    });
}
