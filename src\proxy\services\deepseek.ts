import { type Request, type Request<PERSON><PERSON><PERSON>, Router } from "express";

import { cacheStore } from "@/shared/cache";
import { type DeepSeekKey, keyPool } from "@/shared/key-management";

import { isAllowedModel } from "@/shared/models";
import {
  addKey,
  createPreprocessorMiddleware,
  createResponsesPreprocessorMiddleware,
  finalizeBody,
} from "../middleware/request";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { ipLimiter } from "../rate-limit";

export function getModelsResponse() {
  if (keyPool.getKeyProvider("deepseek").available() === 0) {
    return [];
  }

  const keys = keyPool.getKeyProvider("deepseek").list() as DeepSeekKey[];

  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));
  const models = modelIds
    .filter((id) => isAllowedModel(id))
    .map((id) => ({
      id,
      object: "model",
      created: new Date().getTime(),
      owned_by: "deepseek",
      permission: [],
      root: "deepseek",
      parent: null,
    }));

  return models;
}

const deepseekResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

function removeReasonerStuff(req: Request) {
  if (String(req.body.model).includes("reasoner")) {
    // https://api-docs.deepseek.com/guides/reasoning_model
    delete req.body.presence_penalty;
    delete req.body.frequency_penalty;
    delete req.body.temperature;
    delete req.body.top_p;

    delete req.body.logprobs;
    delete req.body.top_logprobs;
  }
}

function fixChatPrefix(req: Request) {
  const body = req.body as {
    model: string;
    messages: { role: "user" | "assistant" | "system"; content: string }[];
  };
  const lastMessage = body.messages[body.messages.length - 1];

  // Only reasoner model force the last message to be the assistant or user.
  if (body.messages.length && body.model.includes("reasoner")) {
    /**
     * If the last message is not an assistant or user message,
     * then we throw error instead try to edit the data.
     */
    if (lastMessage.role !== "assistant" && lastMessage.role !== "user") {
      throw new Error("Last message role must be assistant or user!");
    }

    /**
     * We should only add the prefix to the last message if it's an assistant message.
     * As it it's the client responsibility to ensure the last message follow the correct format.
     */
    if (lastMessage.role === "assistant") {
      req.body.messages[body.messages.length - 1] = { ...lastMessage, prefix: true };
    }
  }
}

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ReturnType<typeof getModelsResponse>>("deepseek");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await cacheStore.set("deepseek", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const deepseekProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.deepseek.com/beta",
  blockingResponseHandler: deepseekResponseHandler,
});

const deepseekRouter = Router();
deepseekRouter.get("/v1/models", handleModelRequest);

deepseekRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware(
    { inApi: "openai", outApi: "openai", service: "deepseek" },
    { afterTransform: [fixChatPrefix, removeReasonerStuff] }
  ),
  deepseekProxy
);

// Responses API endpoints (DeepSeek is OpenAI-compatible, so it might support these)
deepseekRouter.post(
  "/v1/responses",
  ipLimiter,
  createResponsesPreprocessorMiddleware(),
  deepseekProxy
);

deepseekRouter.get(
  "/v1/responses/:responseId",
  ipLimiter,
  createResponsesPreprocessorMiddleware(),
  deepseekProxy
);

deepseekRouter.post(
  "/v1/responses/:responseId/cancel",
  ipLimiter,
  createResponsesPreprocessorMiddleware(),
  deepseekProxy
);

// Redirect browser requests to the homepage.
deepseekRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");
  if (isBrowser) res.redirect("/");
  else next();
});

export const deepseek = deepseekRouter;
