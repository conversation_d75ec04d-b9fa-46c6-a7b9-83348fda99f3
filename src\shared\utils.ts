import type { Query } from "express-serve-static-core";
import sanitize from "sanitize-html";

export function sanitizeAndTrim(
  input?: string | null,
  options: sanitize.IOptions = {
    allowedTags: [],
    allowedAttributes: {},
  }
) {
  return sanitize((input ?? "").trim(), options);
}

export function redactIp(ip: string) {
  const ipv6 = ip.includes(":");
  return ipv6 ? "redacted:ipv6" : ip.replace(/\.\d+\.\d+$/, ".xxx.xxx");
}

export function assertNever(x: never): never {
  throw new Error(`Called assertNever with argument ${x}.`);
}

export const ObjectTyped = {
  keys: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.keys(obj)) as readonly [keyof TObject];
  },
  entries: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.entries(obj)) as readonly [keyof TObject, TObject[keyof TObject]][];
  },
  values: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.values(obj)) as readonly TObject[keyof TObject][];
  },
};

export function normalizeForDiff(value: unknown): any {
  if (value instanceof Map) {
    return Object.fromEntries(
      Array.from(value.entries()).map(([k, v]) => [k, normalizeForDiff(v)])
    );
  }

  if (value instanceof Set) {
    return Array.from(value.values()).map(normalizeForDiff);
  }

  if (Array.isArray(value)) {
    return value.map(normalizeForDiff);
  }

  if (value !== null && typeof value === "object") {
    return Object.fromEntries(Object.entries(value).map(([k, v]) => [k, normalizeForDiff(v)]));
  }

  return value;
}

export const numberFormat = new Intl.NumberFormat("en-US", {
  style: "decimal",
  maximumFractionDigits: 2,
});

/**
 * A tuple representing the result of an operation that can fail.
 * On success, the tuple is `[data, null]`.
 * On failure, the tuple is `[null, error]`.
 * @template T The type of the data on success.
 * @template E The type of the error on failure, defaults to `Error`.
 */
export type GoResult<T, E = Error> = [T, null] | [null, E];

// This type represents any function that can be called with 'new'
// and produces an instance of type T.
type AnyConstructor<T = {}> = new (...args: any[]) => T;

/**
 * Wraps an async function (a function that returns a Promise) in a try/catch block.
 * This allows for Go-style error handling.
 *
 * @template T The type of the data the Promise resolves to.
 * @param {() => Promise<T>} promiseFn The function that returns the Promise to be executed.
 * @returns {Promise<GoResult<T>>} A Promise that resolves to a tuple: `[data, null]` on success, or `[null, error]` on failure.
 */
export async function tryCatch<T, E>(
  promiseFn: () => Promise<T>,
  errorClass?: AnyConstructor<E>
): Promise<GoResult<T, E | Error>> {
  try {
    const data = await promiseFn();
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }

    if (error instanceof Error) {
      return [null, error];
    }

    return [null, new Error(String(error))];
  }
}

/**
 * Wraps a synchronous function in a try/catch block.
 * This allows for Go-style error handling for synchronous code.
 *
 * @template T The return type of the function.
 * @param {() => T} fn The synchronous function to be executed.
 * @returns {GoResult<T>} A tuple: `[data, null]` on success, or `[null, error]` on failure.
 */
export function tryCatchSync<T, E>(
  fn: () => T,
  errorClass?: AnyConstructor<E>
): GoResult<T, E | Error> {
  try {
    const data = fn();
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }

    if (error instanceof Error) {
      return [null, error];
    }
    return [null, new Error(String(error))];
  }
}
