import type { Request } from "express";
import type { z } from "zod/v4";

import type { APIFormat } from "../key-management";

import { getAnthropicSchema, transformOpenAIToAnthropicChat } from "./anthropic";
import { getGoogleAISchema, transformOpenAIToGoogleAI } from "./google-ai";
import { getOpenAISchema, getOpenAIResponsesSchema } from "./openai";

export type { AnthropicChatMessage } from "./anthropic";
export type { GoogleAIChatMessage } from "./google-ai";
export type { OpenAIChatMessage } from "./openai";

export type APIPair = `openai->${Exclude<APIFormat, "openai">}`;
type TransformerMap = {
  [key in APIPair]?: APIFormatTransformer<any>;
};

export type APIFormatTransformer<Z extends z.ZodType<any, any>> = (
  req: Request
) => Promise<z.infer<Z>>;

export const API_REQUEST_TRANSFORMERS: TransformerMap = {
  "openai->anthropic-chat": transformOpenAIToAnthropicChat,
  "openai->google-ai": transformOpenAIToGoogleAI,
};

export function getAPIRequestSchema<T extends APIFormat>(req: Request, api: T) {
  const schema = {
    "anthropic-chat": getAnthropicSchema,
    openai: getOpenAISchema,
    "openai-response": getOpenAIResponsesSchema,
    "google-ai": getGoogleAISchema,
  } as const;

  return schema[api](req);
}

export function getOpenAIResponsesRequestSchema(req: Request) {
  return getOpenAIResponsesSchema(req);
}
