import type { Request } from "express";
import { z } from "zod/v4";

import { config } from "@/config";
import { getModelFamilyForRequest, getModelMaxOutputLimit } from "../models";

// https://platform.openai.com/docs/api-reference/chat/create
const OpenAIV1ChatContentArraySchema = z.array(
  z.union([
    z.object({ type: z.literal("text"), text: z.string() }),
    z.object({
      type: z.union([z.literal("image"), z.literal("image_url")]),
      image_url: z.object({
        url: z.string(),
        detail: z.enum(["low", "auto", "high"]).default("auto").optional(),
      }),
    }),
  ])
);

const OpenAIToolsSchema = z.object({
  type: z.literal("function"),
  function: z.object({
    name: z
      .string()
      .regex(/[a-zA-Z0-9_]/g, {
        message: "Function name must only contain alphanumeric characters and underscores.",
      })
      .max(64, { message: "Function name must be less than 64 characters." }),

    description: z.string().optional(),
    strict: z.boolean().nullish(),
    parameters: z
      .object({
        type: z.string().optional(),
        properties: z.record(z.string(), z.unknown()),
        required: z.array(z.string()),
        additionalProperties: z.boolean().optional(),
      })
      .optional(),
  }),
});

const OpenAIToolChoiceSchema = z
  .union([
    z.enum(["none", "auto", "required"]),
    z.object({ function: z.object({ name: z.string() }), type: z.literal("function") }),
  ])
  .optional();

export const OpenAIV1ChatCompletionSchema = z.object({
  model: z.string(),
  messages: z.array(
    z.object({
      role: z.enum(["developer", "system", "user", "assistant", "tool", "function"]),
      content: z.union([z.string(), OpenAIV1ChatContentArraySchema]),
      name: z.string().optional(),
      tool_calls: z.array(z.any()).optional(),
      function_call: z.array(z.any()).optional(),
      tool_call_id: z.string().optional(),
      refusal: z.string().nullish(),
    }),
    {
      error: (issue) =>
        issue.input === undefined
          ? "No `messages` found. Ensure you've set the correct completion endpoint."
          : "Messages were not formatted correctly. Refer to the OpenAI Chat API documentation for more information.",
    }
  ),
  temperature: z.number().optional(),
  top_p: z.number().optional(),
  n: z.literal(1, { message: "You may only request a single completion at a time." }).optional(),
  stream: z.boolean().optional().default(false),
  stream_options: z.object({ include_usage: z.boolean().optional() }).nullish(),
  stop: z.union([z.string(), z.array(z.string())]).nullish(),
  store: z.literal(false).default(false).nullish(),

  max_tokens: z.coerce.number().int().nullish(),
  // max_completion_tokens replaces max_tokens in the OpenAI API.
  // for backwards compatibility, we accept both and move the value in
  // max_tokens to max_completion_tokens in proxy middleware.
  max_completion_tokens: z.coerce.number().int().nullish(),

  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  logit_bias: z.any().optional(),
  user: z.string().optional(),
  seed: z.number().int().optional(),

  // Be warned that Azure OpenAI combines these two into a single field.
  // It's the only deviation from the OpenAI API that I'm aware of so I have
  // special cased it in `addAzureKey` rather than expecting clients to do it.
  logprobs: z.boolean().optional(),
  top_logprobs: z.number().int().optional(),
  reasoning_effort: z.enum(["low", "medium", "high"]).optional(),
  response_format: z.record(z.string(), z.unknown()).optional(),
  service_tier: z.string().nullish(),

  // Quickly adding some newer tool usage params
  tools: z.array(OpenAIToolsSchema).optional(),
  functions: z.array(OpenAIToolsSchema).optional(),
  tool_choice: OpenAIToolChoiceSchema.optional(),
  function_choice: OpenAIToolChoiceSchema.optional(),

  parallel_tool_calls: z.boolean().optional(),

  web_search_options: z
    .object({
      search_context_size: z.enum(["low", "high", "medium"]).optional(),
      user_location: z
        .object({
          type: z.literal("approximate"),
          approximate: z.object({
            city: z.string().optional(),
            country: z.string().optional(),
            region: z.string().optional(),
            timezone: z.string().optional(),
          }),
        })
        .nullish(),
    })
    .optional(),
});

export type OpenAIChatMessage = z.infer<typeof OpenAIV1ChatCompletionSchema>["messages"][0];

// OpenAI Responses API Schema
const OpenAIResponsesInputSchema = z.union([
  z.string(),
  z.array(
    z.object({
      role: z.enum(["developer", "system", "user", "assistant", "tool", "function"]),
      content: z.union([
        z.string(),
        z.array(
          z.union([
            z.object({ type: z.literal("text"), text: z.string() }),
            z.object({ type: z.literal("input_text"), text: z.string() }),
            z.object({
              type: z.union([z.literal("image"), z.literal("image_url"), z.literal("input_image")]),
              image_url: z.union([
                z.string(),
                z.object({
                  url: z.string(),
                  detail: z.enum(["low", "auto", "high"]).default("auto").optional(),
                }),
              ]),
            }),
          ])
        ),
      ]),
      name: z.string().optional(),
      tool_calls: z.array(z.any()).optional(),
      function_call: z.array(z.any()).optional(),
      tool_call_id: z.string().optional(),
      refusal: z.string().nullish(),
    })
  ),
]);

const OpenAIResponsesToolSchema = z.union([
  z.object({ type: z.literal("web_search") }),
  z.object({ type: z.literal("web_search_preview") }),
  OpenAIToolsSchema,
]);

export const OpenAIV1ResponsesSchema = z.object({
  model: z.string(),
  input: OpenAIResponsesInputSchema,
  tools: z.array(OpenAIResponsesToolSchema).optional(),
  tool_choice: OpenAIToolChoiceSchema,
  previous_response_id: z.string().optional(),
  store: z.boolean().optional(),
  include: z.array(z.string()).optional(),
  parallel_tool_calls: z.boolean().optional(),
  max_completion_tokens: z.number().int().positive().optional(),
  temperature: z.number().min(0).max(2).optional(),
  top_p: z.number().min(0).max(1).optional(),
  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  seed: z.number().int().optional(),
  stream: z.boolean().optional(),
});

export function getOpenAISchema(req: Request) {
  return OpenAIV1ChatCompletionSchema.transform((data) => {
    const modelFamily = getModelFamilyForRequest(req);

    const maxOutput =
      config.modelFamilySettings.get(modelFamily)?.maxOutput ?? config.defaultGlobalMaxOutput;
    const modelMaxOutput = getModelMaxOutputLimit(req);

    if (!config.allowToolUsage.includes(req.service!)) {
      delete data.tools;
      delete data.tool_choice;
      delete data.functions;
      delete data.function_choice;
    }

    if (
      !data.model.startsWith("o1") ||
      !data.model.startsWith("o3") ||
      !data.model.startsWith("o4") ||
      !data.model.startsWith("codex-")
    ) {
      delete data.reasoning_effort;
    }

    const max_completetion_tokens =
      data.max_completion_tokens ?? data.max_tokens ?? Number.MAX_SAFE_INTEGER;

    // Should use max_completion_tokens instead of max_tokens as max_tokens is deprecated.
    // See: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
    delete data.max_tokens;

    if (!data.stream) delete data.stream_options;

    return {
      ...data,
      max_completion_tokens: Math.min(max_completetion_tokens, modelMaxOutput, maxOutput),
    };
  });
}

export function getOpenAIResponsesSchema(req: Request) {
  return OpenAIV1ResponsesSchema.transform((data) => {
    const modelFamily = getModelFamilyForRequest(req);

    const maxOutput =
      config.modelFamilySettings.get(modelFamily)?.maxOutput ?? config.defaultGlobalMaxOutput;
    const modelMaxOutput = getModelMaxOutputLimit(req);

    if (!config.allowToolUsage.includes(req.service!)) {
      delete data.tools;
      delete data.tool_choice;
    }

    const max_completion_tokens = data.max_completion_tokens ?? Number.MAX_SAFE_INTEGER;

    return {
      ...data,
      max_completion_tokens: Math.min(max_completion_tokens, modelMaxOutput, maxOutput),
    };
  });
}

export function containsImageContent(messages: OpenAIChatMessage[]) {
  for (const message of messages) {
    if (!Array.isArray(message.content)) continue;

    for (const content of message.content) {
      if ("image_url" in content) return true;
    }
  }

  return false;
}
