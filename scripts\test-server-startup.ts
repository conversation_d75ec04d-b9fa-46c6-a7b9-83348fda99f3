#!/usr/bin/env bun
/**
 * Test script to verify the server can start up with the new Responses API implementation
 */

import { spawn } from "child_process";
import { config } from "../src/config";

const PORT = config.port;
const TIMEOUT = 10000; // 10 seconds

async function testServerStartup(): Promise<boolean> {
  console.log("🚀 Testing server startup with Responses API implementation...");

  return new Promise((resolve) => {
    // Start the server
    const server = spawn("bun", ["src/server.ts"], {
      stdio: ["pipe", "pipe", "pipe"],
      env: { ...process.env, NODE_ENV: "test" },
    });

    let serverOutput = "";
    let serverReady = false;
    let timeoutId: NodeJS.Timeout;

    // Set up timeout
    timeoutId = setTimeout(() => {
      if (!serverReady) {
        console.log("❌ Server startup timed out");
        server.kill();
        resolve(false);
      }
    }, TIMEOUT);

    // Listen for server output
    server.stdout?.on("data", (data) => {
      const output = data.toString();
      serverOutput += output;
      
      // Check if server is ready
      if (output.includes("Server ready to accept connections")) {
        serverReady = true;
        clearTimeout(timeoutId);
        console.log("✅ Server started successfully");
        
        // Test basic health check
        testHealthCheck()
          .then((healthy) => {
            server.kill();
            resolve(healthy);
          })
          .catch(() => {
            server.kill();
            resolve(false);
          });
      }
    });

    server.stderr?.on("data", (data) => {
      const error = data.toString();
      if (error.includes("Error") || error.includes("error")) {
        console.log("❌ Server error:", error);
        clearTimeout(timeoutId);
        server.kill();
        resolve(false);
      }
    });

    server.on("error", (error) => {
      console.log("❌ Failed to start server:", error.message);
      clearTimeout(timeoutId);
      resolve(false);
    });

    server.on("exit", (code) => {
      if (!serverReady) {
        console.log(`❌ Server exited with code ${code} before becoming ready`);
        console.log("Server output:", serverOutput);
        clearTimeout(timeoutId);
        resolve(false);
      }
    });
  });
}

async function testHealthCheck(): Promise<boolean> {
  try {
    const response = await fetch(`http://localhost:${PORT}/health`);
    if (response.status === 200) {
      console.log("✅ Health check passed");
      return true;
    } else {
      console.log(`❌ Health check failed with status ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log("❌ Health check failed:", error.message);
    return false;
  }
}

// Run the test
if (import.meta.main) {
  testServerStartup()
    .then((success) => {
      if (success) {
        console.log("🎉 Server startup test passed!");
        process.exit(0);
      } else {
        console.log("💥 Server startup test failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("Test runner failed:", error);
      process.exit(1);
    });
}
