import express, { type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { cacheStore } from "@/shared/cache";
import { keyPool } from "@/shared/key-management";

import { addV1 } from "./add-v1";
import { checkRisuToken } from "./check-risu-token";
import { gatekeeper } from "./gatekeeper";
import { sendErrorToClient } from "./middleware/response/error-generator";

import { anthropic, getModelsResponse as getClaudeModels } from "./services/anthropic";
import { deepseek, getModelsResponse as getDeepSeekModels } from "./services/deepseek";
import { getModelsResponse as getGoogleModels, googleAI } from "./services/google-ai";
import { generateModelList as generateOpenAIModels, openai } from "./services/openai";
import { getModelsResponse as getXAIModels, xai } from "./services/xai";

const proxyRouter = express.Router();

// Remove `expect: 100-continue` header from requests due to incompatibility
// with node-http-proxy.
proxyRouter.use((req, _res, next) => {
  if (req.headers.expect) {
    delete req.headers.expect;
  }
  next();
});

// Apply body parsers.
proxyRouter.use(
  express.json({ limit: "100mb" }),
  express.urlencoded({ extended: true, limit: "100mb" })
);

// Apply auth/rate limits.
proxyRouter.use(gatekeeper);
proxyRouter.use(checkRisuToken);

// Initialize request queue metadata.
proxyRouter.use((req, _res, next) => {
  req.startTime = Date.now();
  req.retryCount = 0;
  next();
});

// Proxy endpoints.
proxyRouter.use("/openai", addV1, openai);
proxyRouter.use("/anthropic", addV1, anthropic);
proxyRouter.use("/google-ai", addV1, googleAI);
proxyRouter.use("/deepseek", addV1, deepseek);
proxyRouter.use("/xai", addV1, xai);

type ModelDataType = {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  permission: unknown[];
  root?: string;
  parent: null;
};

/**
 * <AUTHOR>
 * @see {@link https://gitgud.io/Drago}
 * @see {@linkcode https://gitgud.io/Drago/oai-reverse-proxy/-/blob/main/src/proxy/routes.ts}
 *
 * @description Get a list of all available models with prefix.
 */
function getUniveralModelsWithoutPrefix() {
  if (keyPool.available() === 0) return [];
  let modelsList: ModelDataType[] = [];

  modelsList = modelsList
    .concat(getGoogleModels())
    .concat(getClaudeModels())
    .concat(getDeepSeekModels())
    .concat(getXAIModels())
    .concat(generateOpenAIModels())
    .map((model) => {
      model.id = model.id.split("/").pop() ?? model.id;
      return model;
    });

  return modelsList.toSorted((a, b) => {
    const ownerCompare = a.owned_by.localeCompare(b.owned_by);
    return ownerCompare !== 0 ? ownerCompare : a.id.localeCompare(b.id);
  });
}

const handleModelsRequest: RequestHandler = async (_req, res) => {
  const cache = await cacheStore.get<ModelDataType[]>("universal");

  if (cache) {
    return res.status(200).header("Cache-State", "HIT").json({ object: "list", data: cache });
  }

  const models = getUniveralModelsWithoutPrefix();
  await cacheStore.set("universal", models);

  return res.status(200).header("Cache-State", "MISS").json({ object: "list", data: models });
};

// Universal endpoint for all available models.
const universalRouter = express.Router();

const handleUniversalRequest: RequestHandler = async (req, res, next) => {
  const model = req.body?.model as string;

  // For responses API requests, we need special handling
  if (req.path.includes("/responses")) {
    return handleResponsesUniversalRequest(req, res, next, model);
  }

  // For non-responses API requests, use standard routing
  if (!model) {
    return openai(req, res, next);
  }

  // Default to OpenAI if there is no prefix in the model name.
  switch (true) {
    case model.includes("claude"):
      return anthropic(req, res, next);

    case model.includes("gemini"):
      return googleAI(req, res, next);

    case model.includes("deepseek"):
      return deepseek(req, res, next);

    case model.includes("grok"):
      return xai(req, res, next);

    default:
      return openai(req, res, next);
  }
};

const handleResponsesUniversalRequest = async (req: any, res: any, next: any, model?: string) => {
  // For GET requests (response retrieval) or requests without a model, default to OpenAI
  if (!model || req.method === "GET" || req.path.includes("/cancel")) {
    return openai(req, res, next);
  }

  // For responses API, only OpenAI-compatible providers can handle the full stateful API
  switch (true) {
    case model.includes("grok"):
      // XAI might support responses API since it's OpenAI-compatible
      return xai(req, res, next);

    case model.includes("deepseek"):
      // DeepSeek might support responses API since it's OpenAI-compatible
      return deepseek(req, res, next);

    case model.includes("claude"):
    case model.includes("gemini"):
      // For non-OpenAI-compatible providers, we need to transform to chat completions
      // This loses stateful features but provides basic functionality
      return transformResponsesToChat(req, res, next);

    default:
      // Default to OpenAI for all other models
      return openai(req, res, next);
  }
};

/**
 * Transforms responses API requests to chat completion requests for providers
 * that don't support the full responses API. This loses stateful features
 * but provides basic functionality.
 */
const transformResponsesToChat: RequestHandler = async (req, res, next) => {
  try {
    // Only handle POST requests to create responses
    if (req.method !== "POST" || req.path !== "/v1/responses") {
      return sendErrorToClient({
        req,
        res,
        options: {
          title: "Responses API Not Supported",
          message:
            "This provider does not support the full OpenAI Responses API. Only basic response creation is supported via chat completions transformation.",
          statusCode: 501,
          format: "openai",
          reqId: req.id,
        },
      });
    }

    const { model, input, tools, temperature, max_completion_tokens, ...otherParams } = req.body;

    // Transform responses API input to chat completion messages
    let messages;
    if (typeof input === "string") {
      messages = [{ role: "user", content: input }];
    } else if (Array.isArray(input)) {
      messages = input;
    } else {
      return sendErrorToClient({
        req,
        res,
        options: {
          title: "Invalid Input Format",
          message: "Input must be a string or array of messages",
          statusCode: 400,
          format: "openai",
          reqId: req.id,
        },
      });
    }

    // Transform to chat completion request
    req.body = {
      model,
      messages,
      ...(tools && { tools }),
      ...(temperature && { temperature }),
      ...(max_completion_tokens && { max_tokens: max_completion_tokens }),
      // Note: We lose responses API specific features like previous_response_id, store, etc.
    };

    // Route to appropriate provider based on model
    const modelStr = model as string;
    switch (true) {
      case modelStr.includes("claude"):
        return anthropic(req, res, next);
      case modelStr.includes("gemini"):
        return googleAI(req, res, next);
      default:
        return openai(req, res, next);
    }
  } catch (error) {
    return sendErrorToClient({
      req,
      res,
      options: {
        title: "Transformation Error",
        message: "Failed to transform responses API request to chat completion format",
        statusCode: 500,
        format: "openai",
        reqId: req.id,
      },
    });
  }
};

/**
 * <AUTHOR>
 * @see {@link https://gitgud.io/Drago}
 * @see {@linkcode https://gitgud.io/Drago/oai-reverse-proxy/-/blob/main/src/proxy/routes.ts}
 *
 * @description Universal router for all available models.
 */
universalRouter.get("/v1/models", handleModelsRequest);
universalRouter.post("/v1/chat/completions", handleUniversalRequest);

universalRouter.post("/v1/completions", deepseek);
universalRouter.post("/v1/(messages|complete)", anthropic);

// Responses API endpoints with universal routing
universalRouter.post("/v1/responses", handleUniversalRequest);
universalRouter.get("/v1/responses/:responseId", handleUniversalRequest);
universalRouter.post("/v1/responses/:responseId/cancel", handleUniversalRequest);

universalRouter.post("/v1beta/models", googleAI);
universalRouter.post("/v1beta/openai/chat/completions", googleAI);
universalRouter.post(
  [
    "/:apiVersion(v1alpha|v1beta)/models/:modelId:(generateContent|streamGenerateContent)",
    "/v1beta/openai/chat/completions",
  ],
  addV1,
  googleAI
);

proxyRouter.use("/", addV1, universalRouter);

// Redirect browser requests to the homepage.
proxyRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");

  if (isBrowser) res.redirect("/");
  else next();
});

// Send a fake client error if user specifies an invalid proxy endpoint.
proxyRouter.use((req, res) => {
  sendErrorToClient({
    req,
    res,
    options: {
      title: "Proxy error (HTTP 404 Not Found)",
      message: "The requested proxy endpoint does not exist.",
      model: req.body?.model,
      reqId: req.id,
      format: "unknown",
      obj: {
        proxy_note:
          "Your chat client is using the wrong endpoint. Check the Service Info page for the list of available endpoints.",
        requested_url: req.originalUrl,
      },
    },
  });
});

export { proxyRouter as proxyRouter };
