import type { ProxyResHandlerWithBody } from "./index";

/**
 * Response handler specifically for OpenAI Responses API endpoints.
 * Handles the unique response format and adds proxy metadata.
 */
export const handleResponsesApiResponse: ProxyResHandlerWithBody = async (
  _proxyRes,
  req,
  res,
  body
) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  // For responses API, we want to preserve the original response structure
  // but add our proxy metadata
  const proxyInfo = {
    service: req.service,
    in_api: req.inboundApi,
    out_api: req.outboundApi,
    prompt_transformed: req.inboundApi !== req.outboundApi,
  };

  // Add proxy info without overriding the main response structure
  const responseBody = {
    ...body,
    proxy: proxyInfo,
  };

  res.status(200).json(responseBody);
};
