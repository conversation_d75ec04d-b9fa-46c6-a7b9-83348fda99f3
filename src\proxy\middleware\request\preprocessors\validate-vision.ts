import { config } from "@/config";
import { containsImageContent as containsImageContentAnthropic } from "@/shared/api-schemas/anthropic";
import { containsImageContent as containsImageContentGoogle } from "@/shared/api-schemas/google-ai";
import { containsImageContent as containsImageContentOpenAI } from "@/shared/api-schemas/openai";
import { ForbiddenError } from "@/shared/errors";
import { assertNever } from "@/shared/utils";

import type { RequestPreprocessor } from "../index";

/**
 * Rejects prompts containing images if multimodal prompts are disabled.
 */
export const validateVision: RequestPreprocessor = async (req) => {
  if (req.service === undefined) {
    throw new Error("Request service must be set before validateVision");
  }

  if (req.user?.type === "special") return;
  if (config.allowedVisionServices.includes(req.service)) return;

  // vision not allowed for req's service, block prompts with images
  let hasImage = false;
  switch (req.outboundApi) {
    case "openai":
    case "openai-response":
      // For responses API, check both messages and input fields
      const messages =
        req.body.messages ||
        (req.body.input && Array.isArray(req.body.input) ? req.body.input : []);
      hasImage = containsImageContentOpenAI(messages);
      break;

    case "anthropic-chat":
      hasImage = containsImageContentAnthropic(req.body.messages);
      break;

    case "google-ai":
      hasImage = containsImageContentGoogle(req.body.contents);
      break;

    default:
      assertNever(req.outboundApi);
  }

  if (hasImage) {
    throw new ForbiddenError(
      "Prompts containing images are not permitted. Disable 'Send Inline Images' in your client and try again."
    );
  }
};
