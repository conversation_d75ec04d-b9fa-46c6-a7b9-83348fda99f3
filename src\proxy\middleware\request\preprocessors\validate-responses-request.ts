import { getOpenAIResponsesRequestSchema } from "@/shared/api-schemas";

import type { RequestPreprocessor } from "../index";

/**
 * Validates and transforms the request body for OpenAI Responses API requests.
 * This preprocessor ensures the request conforms to the Responses API schema
 * and applies necessary transformations.
 */
export const validateResponsesRequest: RequestPreprocessor = async (req) => {
  if (req.service === undefined) {
    throw new Error("Request service must be set before validateResponsesRequest");
  }

  // Only validate if this is a responses API request
  if (!req.path.includes("/responses")) {
    return;
  }

  // Skip validation for GET requests (response retrieval) and DELETE/POST cancel requests
  if (req.method === "GET" || req.path.includes("/cancel")) {
    return;
  }

  // Only validate POST requests to /v1/responses (creation)
  if (req.method === "POST" && req.path === "/v1/responses") {
    const schema = getOpenAIResponsesRequestSchema(req);
    req.body = schema.parse(req.body);

    // Set some defaults for responses API
    if (req.body.store === undefined) {
      req.body.store = true; // Default to storing responses for retrieval
    }

    // Ensure model is set for downstream processing
    if (!req.body.model) {
      throw new Error("Model is required for responses API requests");
    }
  }
};
