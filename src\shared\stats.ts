import { config } from "../config";

import type { ModelFamily } from "./models";
import { numberFormat } from "./utils";

// technically slightly underestimates, because completion tokens cost more
// than prompt tokens but we don't track those separately right now
export function getTokenCostUsd(data: {
  modelFamily: ModelFamily;
  inputTokens: number;
  outputTokens: number;
}) {
  const cost = { input: 0, output: 0 };
  switch (data.modelFamily) {
    case "chatgpt-4o":
      // $5.00/1M input tokens
      // $15.00/1M output tokens
      cost.input = 5 / 1_000_000;
      cost.output = 15 / 1_000_000;
      break;

    case "gpt4o":
      // $2.50/1M input tokens
      // $10.00/1M output tokens
      cost.input = 2.5 / 1_000_000;
      cost.output = 10 / 1_000_000;
      break;

    case "gpt4o-mini":
      // $0.15/1M input tokens
      // $0.60/1M output tokens
      cost.input = 0.15 / 1_000_000;
      cost.output = 0.6 / 1_000_000;
      break;

    case "gpt4-turbo":
      // $10.00/1M input tokens
      // $30.00/1M output tokens
      cost.input = 10 / 1_000_000;
      cost.output = 30 / 1_000_000;
      break;

    case "gpt4.5":
      // $75.00/1M input tokens
      // $150.00/1M output tokens
      cost.input = 75 / 1_000_000;
      cost.output = 150 / 1_000_000;
      break;

    case "gpt4.1":
      // $2.00/1M input tokens
      // $8.00/1M output tokens
      cost.input = 2 / 1_000_000;
      cost.output = 8 / 1_000_000;
      break;

    case "gpt4.1-mini":
      // $0.40/1M input tokens
      // $1.60/1M output tokens
      cost.input = 0.4 / 1_000_000;
      cost.output = 1.6 / 1_000_000;
      break;

    case "gpt4.1-nano":
      // $0.10/1M input tokens
      // $0.40/1M output tokens
      cost.input = 0.1 / 1_000_000;
      cost.output = 0.4 / 1_000_000;
      break;

    case "codex-mini":
      // $1.50/1M input tokens
      // $6.00/1M output tokens
      cost.input = 1.5 / 1_000_000;
      cost.output = 6 / 1_000_000;
      break;

    case "o1":
      // $15.00/1M input tokens
      // $60.00/1M output tokens
      cost.input = 15 / 1_000_000;
      cost.output = 60 / 1_000_000;
      break;

    case "o1-pro":
      // $150.00/1M input tokens
      // $600.00/1M output tokens
      cost.input = 150 / 1_000_000;
      cost.output = 600 / 1_000_000;
      break;

    case "o3-pro":
      // $20.00/1M input tokens
      // $80.00/1M output tokens
      cost.input = 20 / 1_000_000;
      cost.output = 80 / 1_000_000;
      break;

    case "o3":
      // $2.00/1M input tokens
      // $8.00/1M output tokens
      cost.input = 2 / 1_000_000;
      cost.output = 8 / 1_000_000;
      break;

    case "o1-mini":
    case "o3-mini":
    case "o4-mini":
      // $1.10/1M input tokens
      // $4.40/1M output tokens
      cost.input = 1.1 / 1_000_000;
      cost.output = 4.4 / 1_000_000;
      break;

    case "gpt4":
      // $30.00/1M input tokens
      // $60.00/1M output tokens
      cost.input = 30 / 1_000_000;
      cost.output = 60 / 1_000_000;
      break;

    case "turbo":
      // $0.15/1M input tokens
      // $0.60/1M output tokens
      cost.input = 0.15 / 1_000_000;
      cost.output = 0.6 / 1_000_000;
      break;

    case "claude-haiku":
      // $0.80/1M input tokens
      // $4.00/1M output tokens
      cost.input = 0.8 / 1_000_000;
      cost.output = 4 / 1_000_000;
      break;

    case "claude-sonnet":
      // $3.00/1M input tokens
      // $15.00/1M output tokens
      cost.input = 3 / 1_000_000;
      cost.output = 15 / 1_000_000;
      break;

    case "claude-opus":
      // $15.00/1M input tokens
      // $75.00/1M output tokens
      cost.input = 15 / 1_000_000;
      cost.output = 75 / 1_000_000;
      break;

    case "deepseek-chat":
      // $0.27/1M input tokens
      // $1.10/1M output tokens
      cost.input = 0.27 / 1_000_000;
      cost.output = 1.1 / 1_000_000;
      break;

    case "deepseek-reasoner":
      // $0.55/1M input tokens
      // $2.19/1M output tokens
      cost.input = 0.55 / 1_000_000;
      cost.output = 2.19 / 1_000_000;
      break;

    case "gemini-flash-lite":
      // $0.10/1M input tokens
      // $0.40/1M output tokens
      cost.input = 0.1 / 1_000_000;
      cost.output = 0.4 / 1_000_000;
      break;

    case "gemini-flash":
      // $0.30/1M input tokens
      // $2.50/1M output tokens
      cost.input = 0.3 / 1_000_000;
      cost.output = 2.5 / 1_000_000;
      break;

    case "gemini-pro":
      if (data.inputTokens <= 200_000) {
        // $1.25/1M input tokens
        // $10.00/1M output tokens
        cost.input = 1.25 / 1_000_000;
        cost.output = 10 / 1_000_000;
      } else {
        // $2.50/1M input tokens
        // $15.00/1M output tokens
        cost.input = 2.5 / 1_000_000;
        cost.output = 15 / 1_000_000;
      }
      break;

    case "grok-2":
      // $2.00/1M input tokens
      // $10.00/1M output tokens
      cost.input = 2 / 1_000_000;
      cost.output = 10 / 1_000_000;
      break;

    case "grok-3":
      // $3.00/1M input tokens
      // $15.00/1M output tokens
      cost.input = 3 / 1_000_000;
      cost.output = 15 / 1_000_000;
      break;

    case "grok-3-fast":
      // $5.00/1M input tokens
      // $25.00/1M output tokens
      cost.input = 5 / 1_000_000;
      cost.output = 25 / 1_000_000;
      break;

    case "grok-3-mini":
      // $0.30/1M input tokens
      // $0.50/1M output tokens
      cost.input = 0.3 / 1_000_000;
      cost.output = 0.5 / 1_000_000;
      break;

    case "grok-3-mini-fast":
      // $0.60/1M input tokens
      // $4.00/1M output tokens
      cost.input = 0.6 / 1_000_000;
      cost.output = 4 / 1_000_000;
      break;
  }

  const pricing = {
    input: cost.input * Math.max(0, data.inputTokens),
    output: cost.output * Math.max(0, data.outputTokens),
  };

  return { ...pricing, total: pricing.input + pricing.output };
}

export function getTokenFormat(tokens: number, cost?: number) {
  const costSuffix = !config.showTokenCosts || !cost ? "" : ` ($${cost.toFixed(6)})`;
  return `${numberFormat.format(tokens)} tokens${costSuffix}`;
}
